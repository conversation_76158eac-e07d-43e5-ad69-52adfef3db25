import HelpCenterSideBar from "~/telexComponents/HelpCenterSidebar";

function Help() {
  return (
    <>
      <div className="md:w-5/6 md:mx-auto md:mt-20 ">
        <div className="md:flex gap-6 min-h-screen">
          <HelpCenterSideBar />

          <div className="md:w-6/12 flex flex-col gap-8 overflow-y-auto pl-6 pr-6">
            <div className="flex flex-col gap-2" id="welcome">
              <h1 className="text-2xl font-bold">Getting Started</h1>
              <section className="text-[#8B8C8D]">
                Welcome to Telex help center, Here, we will provide you with
                detailed answers and articles for your Telex account.{" "}
              </section>
            </div>

            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold">Channel Management</h1>
                <p className="text-[#8B8C8D]">
                  Channels are used to get pushed notifications and can also be
                  used for collaborations between teams
                </p>
              </div>

              <div className="flex flex-col gap-2" id="create">
                <h1 className="text-md font-bold">
                  How to create Channel on Telex?
                </h1>
                <p className="text-[#8B8C8D]">
                  To create a channel on telex, Here is a step by step guide to
                  creating your channel on Telex.
                </p>
                <ul className="flex flex-col gap-4">
                  <li className="list-decimal font-bold ml-4">
                    Log in to your Telex Account.
                  </li>
                  <ul>
                    <li className="list-disc text-[#8B8C8D] ml-8">
                      Access your Telex account by logging in with your
                      credentials or request for a magic link.
                    </li>
                    <li className="list-disc text-[#8b8c8d] ml-8">
                      If you do not have a Telex account, Sign up to get one.
                    </li>
                  </ul>

                  <li className="list-decimal font-bold ml-4">
                    Navigate to the Channels Section
                  </li>
                  <ul>
                    <li className="list-disc text-[#8B8C8D] ml-8">
                    From the main dashboard, locate and click on the 'Open menu' tab.
                    </li>
                    <li className="list-disc text-[#8b8c8d] ml-8">
                    Click on 'view all Channels'.
                    </li>
                    <li className="list-disc text-[#8b8c8d] ml-8">
                    Click the '+new channel button, usually located at the top right corner of the channels section.
                    </li>
                  </ul>

                  <li className="list-decimal font-bold ml-4">
                    Name Your Channel
                  </li>
                  <ul>
                    <li className="list-disc text-[#8B8C8D] ml-8">
                    A modal will pop up when you click on '+ new channel' button asking for the name of your channel.
                    </li>
                    <li className="list-disc text-[#8b8c8d] ml-8">
                    Enter a descriptive name for your channel and set the channel visibility to your preference.
                    </li>
                    <li className="list-disc text-[#8b8c8d] ml-8">
                    Click on 'create channel' button located at the down right corner of the modal.
                    </li>
                    <li className="list-disc text-[#8b8c8d] ml-8">
                    Your have successfully creaed a new channel which will appear on the list of channels available in your organisation.
                    </li>
                  </ul>

                  <li className="list-decimal font-bold ml-4">
                    Invite Members
                  </li>
                  <ul>
                    <li className="list-disc text-[#8B8C8D] ml-8">
                      Add team members who should have access to this channel
                      and receive notifications.
                    </li>
                    <li className="list-disc text-[#8b8c8d] ml-8">
                      Enter each team member email address where it is required
                      to invite them.
                    </li>
                  </ul>
                </ul>
                <p className="text-[#8b8c8d]">
                  These four simple steps will help you quickly set up a channel
                  on Telex, ensuring you stay informed about critical events in
                  your organization’s applications.
                </p>
              </div>

              <div className="flex flex-col gap-6" id="more">
                <div className="flex flex-col gap-2">
                  <h1 className="text-md font-bold">
                    Can more than one channel be created on telex
                  </h1>
                  <p className="text-[#8B8C8D]">
                    Yes, users are allowed to have more than 1 channel and a
                    minimum of 10 channels on the free trial
                  </p>
                </div>
              </div>

              <div className="flex flex-col gap-6" id="members">
                <div className="flex flex-col gap-2">
                  <h1 className="text-md font-bold">
                    How many members are are allowed per channel
                  </h1>
                  <p className="text-[#8B8C8D]">
                    Users are allowed to have as much members as possible in a
                    channel (there's no limit on the current freemium plan).
                  </p>
                </div>
              </div>

              <div className="flex flex-col gap-6" id="retrieved">
                <div className="flex flex-col gap-2">
                  <h1 className="text-md font-bold">
                    Can a channel be retrieved after it is deleted
                  </h1>
                  <p className="text-[#8B8C8D]">
                    No, all channels deleted can not be retrieved
                  </p>
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-2">
                <h1 className="text-md font-bold">Apps Integration</h1>
                <p className="text-[#8B8C8D]">
                  you can integrate third party apps into telex
                </p>
              </div>

              <div className="flex flex-col gap-2" id="app">
                <h1 className="text-md font-bold">
                  How do i integrate apps with telex
                </h1>
                <p className="text-[#8B8C8D]">
                This should be a step by step guide
                </p>
              </div>
            </div>

            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-2">
                <h1 className="text-md font-bold">User roles and permission</h1>
                <p className="text-[#8B8C8D]">User roles and permission</p>
              </div>

              
            </div>

            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-2">
                <h1 className="text-md font-bold">Workflow</h1>
                <p className="text-[#8B8C8D]">Custom Workflow</p>
              </div>
              <div className="flex flex-col gap-2" id="workflow">
                <h1 className="text-md font-bold">
                  Can i create custom workflows
                </h1>
                <p className="text-[#8B8C8D]">Yes, Telex allows you create workflows with your preferred agents</p>
                <p className="text-[#8B8C8D]">
                  a step by step guide should follow
                </p>
              </div>
            </div>

            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-2">
                <h1 className="text-md font-bold">Teams</h1>
                <p className="text-[#8B8C8D]">Invite people or Al agents to your organisation and start working.</p>
              </div>

              <div className="flex flex-col gap-2" id="team">
                <h1 className="text-md font-bold">
                  How do i manage my teams roles and permissions
                </h1>
                <p className="text-[#8B8C8D]">
                  This should be a step by step guide
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Help;

// "use client";

// import { useEffect, useState } from "react";
// import { GetRequest } from "~/utils/request";
// import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
// import { Terminal } from "lucide-react";
// import Link from "next/link";
// import HelpPaginationDesktop from "~/telexComponents/HelpPageComponents/HelpPaginationDesktop";
// import HelpPageHeader from "~/telexComponents/HelpPageComponents/HelpPageHeader";
// import HelpPaginationMobile from "~/telexComponents/HelpPageComponents/HelpPaginationMobile";
// import HelpPreLoader from "~/telexComponents/HelpPageComponents/HelpPreLoader";
// import HelpPageSearch from "~/telexComponents/HelpPageComponents/HelpPageSearch";

// interface Article {
//   id: string;
//   name: string;
//   description: string;
//   articles_len: string;
//   created_at: string;
//   updated_at: string;
// }

// export default function HelpCenter() {
//   const [loading, setLoading] = useState(true);
//   const [data, setData] = useState<Article[]>([]);
//   const [currentPage, setCurrentPage] = useState(1);

//   const postsPerPage = 5;
//   useEffect(() => {
//     const fetchCategories = async () => {
//       setLoading(true);
//       try {
//         const token = localStorage.getItem("token");
//         if (!token) throw new Error("No token found");

//         const response = await GetRequest("/help-center/categories", token);
//         if (response?.status === 200 || response?.status === 201) {
//           const responseData = response.data;
//           if (
//             responseData?.status === "success" &&
//             responseData?.status_code === 200
//           ) {
//             setData(responseData?.data || []);
//           }
//         } else {
//           console.error("Failed to fetch data: ", response);
//         }
//       } catch (error) {
//         console.error("Error fetching data: ", error);
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchCategories();
//   }, []);

//   const lastPostIndex = currentPage * postsPerPage;
//   const firstPostIndex = lastPostIndex - postsPerPage;
//   const currentPosts = data.slice(firstPostIndex, lastPostIndex);

//   if (loading) {
//     return <HelpPreLoader />;
//   }

//   return (
//     <main>
//       <div className="container mx-auto p-4 max-w-[852px]">
//         <HelpPageHeader />

//         <div>
//           {currentPosts.length == 0 && (
//             <div className="border p-5 rounded-md text-center">
//               Click <span className="font-bold text-[#8055F8]">Login</span>{" "}
//               above to load{" "}
//               <span className="font-bold text-[#8055F8]"> Help Center</span>{" "}
//               contents
//             </div>
//           )}

//           <HelpPageSearch />

//           {currentPosts.map((article) => (
//             <Alert
//               className="bg-[#FAFAFA] hover:bg-[#F6F6F6] border-[0.5px] border-opacity-50 border-[#8860F8] p-[15px] rounded-[10px] my-7"
//               key={article.id}
//             >
//               <Link href={`/help/${article.id}`}>
//                 <div className="flex gap-4 items-center leading-[140%]">
//                   <Terminal className="h-8 w-8 bg-[#8860F8] text-[#F9FAFB] rounded-[6px] p-2" />
//                   <div>
//                     <AlertTitle className="text-[16px] font-bold">
//                       {article.name}
//                     </AlertTitle>
//                     <AlertDescription className="text-[16px]">
//                       {article.description}
//                     </AlertDescription>
//                     <AlertDescription className="text-[#8B8C8D] text-[12px]">
//                       {article.articles_len} Articles
//                     </AlertDescription>
//                   </div>
//                 </div>
//               </Link>
//             </Alert>
//           ))}
//         </div>
//       </div>
//       <div>
//         {data.length > postsPerPage && (
//           <HelpPaginationDesktop
//             totalPosts={data.length}
//             postsPerPage={postsPerPage}
//             currentPage={currentPage}
//             setCurrentPage={setCurrentPage}
//           />
//         )}
//       </div>

//       <div className="flex justify-center lg:hidden">
//         {data.length > postsPerPage && (
//           <HelpPaginationMobile
//             totalPosts={data.length}
//             postsPerPage={postsPerPage}
//             currentPage={currentPage}
//             setCurrentPage={setCurrentPage}
//           />
//         )}
//       </div>
//     </main>
//   );
// }
