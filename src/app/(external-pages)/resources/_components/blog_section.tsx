"use client";

import { ArrowRight, Search } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";

const blogPosts = [
  {
    type: "ESSENTIAL GUIDE",
    tagColor: "#E36914",
    bgColor: "#FEF1E8",
    date: "29.03.2025",
    title: "Getting started (Account Creator & Invited User)",
    image: "/images/article_content_orange.svg",
    action: "Read guide",
    link: "/resources",
  },
  {
    type: "ESSENTIAL GUIDE",
    tagColor: "#2E8DFF",
    bgColor: "#E6F1FF",
    date: "26.03.2025",
    title: "Managing Your Profile Preferences",
    image: "/images/article_content_blue.svg",
    action: "Read guide",
    link: "/resources",
  },
  {
    type: "BLOG",
    tagColor: "#6868F7",
    bgColor: "#F1F1FE",
    date: "24.03.2025",
    title: "Why Downtime is Killing Your Conversions",
    image: "/images/article_content_purple.svg",
    action: "Read article",
    link: "/resources",
  },
  {
    type: "BLOG",
    tagColor: "#009143",
    bgColor: "#F2FCF7",
    date: "22.03.2025",
    title: "Test Stripe Webhooks in Real-Time",
    image: "/images/article_content_green.svg",
    action: "Read article",
    link: "/resources",
  },
  {
    type: "ESSENTIAL GUIDE",
    tagColor: "#2E8DFF",
    bgColor: "#FFFFFF",
    date: "21.03.2025",
    title: "Setting Up Notification Preferences",
    image: "/images/article_content_blue.svg",
    action: "Read guide",
    link: "/resources",
  },
  {
    type: "GUIDE",
    tagColor: "#009143",
    bgColor: "#FFFFFF",
    date: "19.03.2025",
    title: "Find the Right Agent For Your Need",
    image: "/images/article_content_green.svg",
    action: "Read guide",
    link: "/resources",
  },
  {
    type: "BLOG",
    tagColor: "#E36914",
    bgColor: "#FFFFFF",
    date: "16.03.2025",
    title: "Monitor AWS EC2 Health in Real-Time",
    image: "/images/article_content_orange.svg",
    action: "Read article",
    link: "/resources",
  },
  {
    type: "BLOG",
    tagColor: "#344054",
    bgColor: "#F9FAFB",
    date: "12.03.2025",
    title: "Catch MongoDB Downtime Before Clients",
    image: "/images/article_content_gray.svg",
    action: "Read article",
    link: "/resources/catch-mongodb-downtime-before-clients",
  },
  {
    type: "ESSENTIAL GUIDE",
    tagColor: "#E36914",
    bgColor: "#FEF1E8",
    date: "10.03.2025",
    title: "Managing Your Channels as an Org Admin",
    image: "/images/article_content_orange.svg",
    action: "Read guide",
    link: "/resources",
  },
  {
    type: "BLOG",
    tagColor: "#6868F7",
    bgColor: "#F1F1FE",
    date: "7.03.2025",
    title: "Why Downtime is Killing Your Conversions",
    image: "/images/article_content_purple.svg",
    action: "Read article",
    link: "/resources",
  },
  {
    type: "ESSENTIAL GUIDE",
    tagColor: "#2E8DFF",
    bgColor: "#FFFFFF",
    date: "7.03.2025",
    title: "How to Automate Tasks With Workflows",
    image: "/images/article_content_blue.svg",
    action: "Read guide",
    link: "/resources",
  },
  {
    type: "BLOG",
    tagColor: "#6868F7",
    bgColor: "#FFFFFF",
    date: "4.03.2025",
    title: "Why Downtime is Killing Your Conversions",
    image: "/images/article_content_purple.svg",
    action: "Read article",
    link: "/resources",
  },
];

const BlogSection = () => {
  const [activeFilter, setActiveFilter] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");

  const handleFilterClick = (filter: string) => {
    setActiveFilter(filter);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const filteredPosts = blogPosts.filter((post) => {
    const matchesType =
      activeFilter === "All" ||
      (activeFilter === "Blog" && post.type === "BLOG") ||
      (activeFilter === "Guides" &&
        (post.type === "GUIDE" || post.type === "ESSENTIAL GUIDE"));

    const matchesSearch = post.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());

    return matchesType && matchesSearch;
  });

  const buttonStyle = (label: string) =>
    `w-[84px] h-11 flex items-center justify-center text-sm font-semibold rounded-2xl transition-all ${
      activeFilter === label ? "bg-[#101828] text-white" : ""
    }`;

  return (
    <section className="xl:px-[100px] px-16 py-20 space-y-10">
      {/* Filters and Search */}
      <div className="flex flex-col items-center gap-8 xl:flex-row md:justify-between xl:gap-0">
        <div className="flex items-center bg-[#F9FAFB] border border-[#E6EAEF] rounded-[17px] p-1">
          <button
            className={buttonStyle("All")}
            onClick={() => handleFilterClick("All")}
          >
            All
          </button>
          <button
            className={buttonStyle("Blog")}
            onClick={() => handleFilterClick("Blog")}
          >
            Blog
          </button>
          <button
            className={buttonStyle("Guides")}
            onClick={() => handleFilterClick("Guides")}
          >
            Guides
          </button>
        </div>
        <div className="md:w-[392px] h-[40px] flex items-center text-[#667085] gap-1.5 border border-[#E6EAEF] rounded-md p-2.5">
          <Search size={16} />
          <input
            type="text"
            placeholder="Search for a topic"
            value={searchQuery}
            onChange={handleSearchChange}
            className="w-full outline-none bg-transparent"
          />
        </div>
      </div>

      {/* Blog Grid or No Results */}
      {filteredPosts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-12 md:gap-8">
          {filteredPosts.map((post, index) => (
            <div
              key={index}
              className="relative flex flex-col gap-8 border border-[#E4E7EC] rounded-[10px] px-5 py-6 shadow-md overflow-hidden"
              style={{ backgroundColor: post.bgColor }}
            >
              <div className="flex items-center justify-between text-[10px] md:text-xs font-medium">
                <h3 style={{ color: post.tagColor }}>{post.type}</h3>
                <p className="text-[#667085]">{post.date}</p>
              </div>
              <div className="relative z-10 h-[140px] md:h-[188px]">
                <h2 className="text-[#101828] font-medium text-xl md:text-2xl">
                  {post.title}
                </h2>
              </div>
              <Link
                href={post.link}
                className="relative z-10 flex items-center text-[#344054] text-xs md:text-sm font-semibold gap-1 cursor-pointer transition-all duration-300 hover:underline"
              >
                <p>{post.action}</p>
                <ArrowRight size={16} />
              </Link>
              <Image
                src={post.image}
                alt="Article Content"
                width={126}
                height={126}
                className="absolute w-[100px] h-[100px] md:w-[126px] md:h-[126px] bottom-0 right-0"
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="w-[250px] md:w-full flex flex-col items-center text-center text-[#667085]">
          <Image
            src="/images/bot-gray.svg"
            alt="Gray Bot"
            width={200}
            height={400}
            className="opacity-10"
          />
          <h2 className="mb-2">No more Topics fits your search</h2>
          <p>Try and search again!!!</p>
        </div>
      )}
    </section>
  );
};

export default BlogSection;
